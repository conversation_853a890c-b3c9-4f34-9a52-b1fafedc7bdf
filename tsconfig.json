{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "strict": true, "noFallthroughCasesInSwitch": true, "noEmit": true, "jsxImportSource": "bippy/dist"}, "include": ["src", "vite.config.ts"]}